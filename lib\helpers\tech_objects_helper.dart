import 'package:eam/be/EQUIP_HEADER.dart';
import 'package:eam/be/EQUIP_LOC_ADD.dart';
import 'package:eam/be/EQUIP_MANUFACTURE.dart';
import 'package:eam/be/EQUIP_MEAS_POINT.dart';
import 'package:eam/be/EQUIP_ORG_ADD.dart';
import 'package:eam/be/EQUIP_SUB_EQUIP.dart';
import 'package:eam/be/FORM_HEADER.dart';
import 'package:eam/be/FUNCLOC_CLASS.dart';
import 'package:eam/be/FUNCLOC_LOC_ADD.dart';
import 'package:eam/be/FUNCLOC_MEAS_POINT.dart';
import 'package:eam/be/FUNCLOC_ORG_ADD.dart';
import 'package:eam/be/FUNCLOC_SUB_EQUIP.dart';
import 'package:eam/be/FUNCLOC_SUB_LOC.dart';
import 'package:eam/be/FUNC_LOC_HEADER.dart';
import 'package:eam/be/MATERIAL_BOM.dart';
import 'package:eam/be/MATERIAL_HEADER.dart';
import 'package:eam/be/TECH_OBJECT_HISTORY_INPUT_HEADER.dart';
import 'package:eam/utils/app_constants.dart';
import 'package:eam/utils/constants.dart';
import 'package:eam/utils/utils.dart';
import 'package:logger/logger.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

class TechObjectsHelper {
  static List<EQUIP_SUB_EQUIP> selectedSubEquips = [];

  static Future<int> getEquipmentsCount() async {
    var query = "SELECT COUNT(*) AS count FROM ${EQUIP_HEADER.TABLE_NAME}";
    try {
      List<Map<String, dynamic>>? dataList =
          await AppDatabaseManager().execute(query);
      if (dataList != null && dataList.length > 0) {
        return (dataList[0]["count"]);
      }
    } catch (e) {
      Logger.logError("TechObjectsHelper", 'getEquipmentsCount', e.toString());
    }
    return 0;
  }

  static Future<int> getFunctionalLocationsCount() async {
    var query = "SELECT COUNT(*) AS count FROM ${FUNC_LOC_HEADER.TABLE_NAME}";
    try {
      List<Map<String, dynamic>>? dataList =
          await AppDatabaseManager().execute(query);
      if (dataList != null && dataList.length > 0) {
        return (dataList[0]["count"]);
      }
    } catch (e) {
      Logger.logError(
          "TechObjectsHelper", 'getFunctionalLocationsCount', e.toString());
    }
    return 0;
  }

  static getOrderedEquipList(List<EQUIP_HEADER> equipments) {
    if (equipments.length > 0) {
      equipments.sort((a, b) {
        int sComp = a.super_func_loc!.compareTo(b.super_func_loc!);
        if (sComp != 0) {
          return sComp;
        }
        return a.equnr!.compareTo(b.equnr!);
      });
    }
  }

  static getOrderedFunctionalLocationList(List<FUNC_LOC_HEADER> fLocs) {
    if (fLocs.length > 0) {
      fLocs.sort((a, b) {
        int sComp = a.func_loc!.toString().compareTo(b.func_loc!.toString());
        if (sComp != 0) {
          return sComp;
        }
        return a.shtxt!.toString().compareTo(b.shtxt!.toString());
      });
    }
  }

  static Future<EQUIP_MANUFACTURE?> getAddInfoManufacturer(
      {required String equipNo}) async {
    String whereClause = EQUIP_MANUFACTURE.FIELD_EQUNR + "= '" + equipNo + "'";
    try {
      List dataList = await AppDatabaseManager().select(
          DBInputEntity(EQUIP_MANUFACTURE.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        return EQUIP_MANUFACTURE.fromJson(dataList[0]);
      }
    } catch (e) {
      Logger.logError(
          "TechObjectsHelper", 'getAddInfoManufacturer', e.toString());
    }

    return null;
  }

  static Future<EQUIP_LOC_ADD?> getAddInfoLocation(
      {required String equipNo}) async {
    String whereClause = EQUIP_LOC_ADD.FIELD_EQUNR + "= '" + equipNo + "'";
    try {
      List dataList = await AppDatabaseManager().select(
          DBInputEntity(EQUIP_LOC_ADD.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        return EQUIP_LOC_ADD.fromJson(dataList[0]);
      }
    } catch (e) {
      Logger.logError(
          "TechObjectsHelper", 'getAddInfoManufacturer', e.toString());
    }

    return null;
  }

  static Future<EQUIP_ORG_ADD?> getAddInfoOrganization(
      {required String equipNo}) async {
    String whereClause = EQUIP_ORG_ADD.FIELD_EQUNR + "= '" + equipNo + "'";
    try {
      List dataList = await AppDatabaseManager().select(
          DBInputEntity(EQUIP_ORG_ADD.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        return EQUIP_ORG_ADD.fromJson(dataList[0]);
      }
    } catch (e) {
      Logger.logError(
          "TechObjectsHelper", 'getAddInfoManufacturer', e.toString());
    }

    return null;
  }

  static Future<List<EQUIP_SUB_EQUIP>> getSelectedSubEquips(
      String eqNum) async {
    selectedSubEquips = await getSubEquipments(equipNo: eqNum);
    return selectedSubEquips;
  }

  static Future<List<EQUIP_SUB_EQUIP>> getSubEquipments(
      {required String equipNo}) async {
    String whereClause = EQUIP_SUB_EQUIP.FIELD_EQUNR + "= '" + equipNo + "'";
    List<EQUIP_SUB_EQUIP> subEquipList = [];
    try {
      List dataList = await AppDatabaseManager().select(
          DBInputEntity(EQUIP_SUB_EQUIP.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        for (var data in dataList) {
          subEquipList.add(EQUIP_SUB_EQUIP.fromJson(data));
        }
      }
    } catch (e) {
      Logger.logError("TechObjectsHelper", 'getSubEquipments', e.toString());
    }

    return subEquipList;
  }

  static Future<MATERIAL_HEADER?> getSubEquipMaterialHeader(
      {required String matNo}) async {
    String whereClause =
        MATERIAL_HEADER.FIELD_MATERIAL_NO + " = '" + matNo + "'";
    try {
      List dataList = await AppDatabaseManager().select(
          DBInputEntity(MATERIAL_HEADER.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        return MATERIAL_HEADER.fromJson(dataList[0]);
      }
    } catch (e) {
      Logger.logError(
          "TechObjectsHelper", 'getSubEquipMaterialHeader', e.toString());
    }

    return null;
  }

  static Future<EQUIP_HEADER?> getEquipment({required String lid}) async {
    try {
      List dataList = await AppDatabaseManager().select(
          DBInputEntity(EQUIP_HEADER.TABLE_NAME, {})
            ..setWhereClause(FieldLid + " = '" + lid + "'"));
      if (dataList.length > 0) {
        return EQUIP_HEADER.fromJson(dataList[0]);
      }
    } catch (e) {
      Logger.logError("TechObjectsHelper", 'getEquipment', e.toString());
    }

    return null;
  }

  static Future<EQUIP_HEADER?> getEquipmentHeaderForDetail(
      {required String equipNo}) async {
    String whereClause = EQUIP_HEADER.FIELD_EQUNR + "= '" + equipNo + "'";
    try {
      List dataList = await AppDatabaseManager().select(
          DBInputEntity(EQUIP_HEADER.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        return EQUIP_HEADER.fromJson(dataList[0]);
      }
    } catch (e) {
      Logger.logError(
          "TechObjectsHelper", 'getEquipmentHeaderForDetail', e.toString());
    }

    return null;
  }

  static Future<Result?> getHistoryInSyncModeForEquip(
      {required List<String> params}) async {
    Result? result;
    try {
      String userId = await Utils.getUserId();
      TECH_OBJECT_HISTORY_INPUT_HEADER history =
          TECH_OBJECT_HISTORY_INPUT_HEADER(user_id: userId);
      // history.user_id = await Utils.getUserId();
      history.equip_no = params[0];
      history.func_loc = params[1];
      Map<String, dynamic> data = {
        "TECH_OBJECT_HISTORY_INPUT": [
          {"TECH_OBJECT_HISTORY_INPUT_HEADER": history.toJson()}
        ]
      };
      result = await (SyncEngine()
            ..isAutoSave(true)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.pull))
          .send(
              umpApplicationFunctionName:
                  AppConstants.PA_GET_TECH_OBJECT_HISTORY,
              dataObject: data);
      return result;
    } catch (e) {
      Logger.logError(
          "TechObjectsHelper", 'getHistoryInSyncModeForEquip', e.toString());
    }
  }

  static Future<FUNCLOC_LOC_ADD?> getAddInfoLocationFLoc(
      {required String fLoc}) async {
    String whereClause = FUNCLOC_LOC_ADD.FIELD_FUNC_LOC + "= '" + fLoc + "'";
    try {
      List dataList = await AppDatabaseManager().select(
          DBInputEntity(FUNCLOC_LOC_ADD.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        return FUNCLOC_LOC_ADD.fromJson(dataList[0]);
      }
    } catch (e) {
      Logger.logError(
          "TechObjectsHelper", 'getAddInfoLocationFLoc', e.toString());
    }

    return null;
  }

  static Future<FUNCLOC_ORG_ADD?> getAddInfoOrganizationFLoc(
      {required String fLoc}) async {
    String whereClause = FUNCLOC_ORG_ADD.FIELD_FUNC_LOC + "= '" + fLoc + "'";
    try {
      List dataList = await AppDatabaseManager().select(
          DBInputEntity(FUNCLOC_ORG_ADD.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        return FUNCLOC_ORG_ADD.fromJson(dataList[0]);
      }
    } catch (e) {
      Logger.logError(
          "TechObjectsHelper", 'getAddInfoOrganizationFLoc', e.toString());
    }

    return null;
  }

  static Future<List<FUNCLOC_SUB_EQUIP>> getSelectedFLocSubEquips(
      {required String funcLoc}) async {
    String whereClause =
        FUNCLOC_SUB_EQUIP.FIELD_FUNC_LOC + "= '" + funcLoc + "'";
    List<FUNCLOC_SUB_EQUIP> subEquipList = [];
    try {
      List dataList = await AppDatabaseManager().select(
          DBInputEntity(FUNCLOC_SUB_EQUIP.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        for (var data in dataList) {
          subEquipList.add(FUNCLOC_SUB_EQUIP.fromJson(data));
        }
      }
    } catch (e) {
      Logger.logError(
          "TechObjectsHelper", 'getSelectedFLocSubEquips', e.toString());
    }

    return subEquipList;
  }

  static Future<List<FUNCLOC_SUB_LOC>> getSelectedFLocSubFLocs(
      {required String funcLoc}) async {
    String whereClause = FUNCLOC_SUB_LOC.FIELD_FUNC_LOC + "= '" + funcLoc + "'";
    List<FUNCLOC_SUB_LOC> subFLocsList = [];
    try {
      List dataList = await AppDatabaseManager().select(
          DBInputEntity(FUNCLOC_SUB_LOC.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        for (var data in dataList) {
          subFLocsList.add(FUNCLOC_SUB_LOC.fromJson(data));
        }
      }
    } catch (e) {
      Logger.logError(
          "TechObjectsHelper", 'getSelectedFLocSubFLocs', e.toString());
    }

    return subFLocsList;
  }

  static Future<FUNC_LOC_HEADER?> getFLoc({required String lid}) async {
    try {
      List dataList = await AppDatabaseManager().select(
          DBInputEntity(FUNC_LOC_HEADER.TABLE_NAME, {})
            ..setWhereClause(FieldLid + " = '" + lid + "'"));
      if (dataList.length > 0) {
        return FUNC_LOC_HEADER.fromJson(dataList[0]);
      }
    } catch (e) {
      Logger.logError("TechObjectsHelper", 'getFLoc', e.toString());
    }

    return null;
  }

  static Future<bool?> isSubFunctionalLocExistsInDB(
      {required String subFLoc}) async {
    var query =
        "SELECT COUNT(*) AS count FROM ${FUNC_LOC_HEADER.TABLE_NAME} WHERE " +
            FUNC_LOC_HEADER.FIELD_FUNC_LOC +
            "= '" +
            subFLoc +
            "'";
    try {
      List<Map<String, dynamic>>? dataList =
          await AppDatabaseManager().execute(query);
      if (dataList != null && dataList.length > 0) {
        return (dataList[0]["count"]) > 0;
      } else {
        return false;
      }
    } catch (e) {
      Logger.logError(
          "TechObjectsHelper", 'isSubFunctionalLocExistsInDB', e.toString());
    }
  }

  static Future<bool?> isSubEquipmentExistsInDB(
      {required String equipNo}) async {
    var query =
        "SELECT COUNT(*) AS count FROM ${EQUIP_HEADER.TABLE_NAME} WHERE " +
            EQUIP_HEADER.FIELD_EQUNR +
            "= '" +
            equipNo +
            "'";
    try {
      List<Map<String, dynamic>>? dataList =
          await AppDatabaseManager().execute(query);
      if (dataList != null && dataList.length > 0) {
        return (dataList[0]["count"]) > 0;
      } else {
        return false;
      }
    } catch (e) {
      Logger.logError(
          "TechObjectsHelper", 'isSubFunctionalLocExistsInDB', e.toString());
    }
  }

  static Future<FUNC_LOC_HEADER?> getFLocHeader(
      {required String fLocVal}) async {
    String whereClause = FUNC_LOC_HEADER.FIELD_FUNC_LOC + "= '" + fLocVal + "'";
    try {
      List dataList = await AppDatabaseManager().select(
          DBInputEntity(FUNC_LOC_HEADER.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        return FUNC_LOC_HEADER.fromJson(dataList[0]);
      }
    } catch (e) {
      Logger.logError("TechObjectsHelper", 'getFLocHeader', e.toString());
    }

    return null;
  }

  static Future<int> getFormsCount() async {
    var query = "SELECT COUNT(DISTINCT(" +
        FORM_HEADER.FIELD_FORM_NAME +
        " )) AS count FROM " +
        FORM_HEADER.TABLE_NAME +
        "";
    try {
      List<Map<String, dynamic>>? dataList =
          await AppDatabaseManager().execute(query);
      if (dataList != null && dataList.length > 0) {
        return int.parse(dataList[0]["count"]);
      }
    } catch (e) {
      Logger.logError("TechObjectsHelper", 'getFormsCount', e.toString());
    }
    return 0;
  }

  static Future<List<FUNC_LOC_HEADER>> getAllFuncLocHeaders() async {
    List<FUNC_LOC_HEADER> funcLocHeaders = [];
    try {
      List dataList = await AppDatabaseManager()
          .select(DBInputEntity(FUNC_LOC_HEADER.TABLE_NAME, {}));
      if (dataList.length > 0) {
        for (var data in dataList) {
          funcLocHeaders.add(FUNC_LOC_HEADER.fromJson(data));
        }
      }
    } catch (e) {
      Logger.logError(
          "TechObjectsHelper", 'getAllFuncLocHeaders', e.toString());
    }
    return funcLocHeaders;
  }

  static Future<List<EQUIP_HEADER>> getAllEquipmentsHeaders() async {
    List<EQUIP_HEADER> equipHeaders = [];
    try {
      List<dynamic> dataList = await AppDatabaseManager()
          .select(DBInputEntity(EQUIP_HEADER.TABLE_NAME, {}));
      if (dataList.length > 0) {
        for (var data in dataList) {
          equipHeaders.add(EQUIP_HEADER.fromJson(data));
        }
      }
    } catch (e) {
      Logger.logError(
          'TechObjectsHelper', 'getAllEquipmentsHeaders', e.toString());
    }

    return equipHeaders;
  }

  static Future<List<MATERIAL_BOM>> getMaterialBom(
      {required String materialNumber}) async {
    List<MATERIAL_BOM> list = [];
    try {
      // List<dynamic> dataList2 = await AppDatabaseManager()
      //     .execute('SELECT * FROM ${MATERIAL_BOM.TABLE_NAME}');

      String _query = 'SELECT * FROM ${MATERIAL_BOM.TABLE_NAME} '
          'WHERE ${MATERIAL_BOM.FIELD_MATERIAL_NO} = "$materialNumber"';

      List<dynamic> dataList = await AppDatabaseManager().execute(_query);

      if (dataList.length > 0) {
        for (var data in dataList) {
          list.add(MATERIAL_BOM.fromJson(data));
        }
      }
    } catch (e) {
      Logger.logError('TechObjectsHelper', 'getMaterialBom', e.toString());
    }
    return list;
  }

  static Future<List<FUNCLOC_CLASS>> getAllFunctionalLocatiionClassfor(
      String funcLoc) async {
    try {
      String whereClause = "${FUNCLOC_CLASS.FIELD_FUNC_LOC} = '$funcLoc'";
      List<FUNCLOC_CLASS> result = (await AppDatabaseManager().select(
              DBInputEntity(FUNCLOC_CLASS.TABLE_NAME, {})
                ..setWhereClause(whereClause)))
          .map((e) => FUNCLOC_CLASS.fromJson(e))
          .toList();
      return result;
    } catch (e) {
      throw e;
    }
  }

  static Future<List<FUNCLOC_MEAS_POINT>> getAllMeasPointsOfFuncLoc(
      String funcLoc) async {
    try {
      String whereClause = "${FUNCLOC_MEAS_POINT.FIELD_FUNC_LOC} = '$funcLoc'";
      List<FUNCLOC_MEAS_POINT> result = (await AppDatabaseManager().select(
              DBInputEntity(FUNCLOC_MEAS_POINT.TABLE_NAME, {})
                ..setWhereClause(whereClause)))
          .map((e) => FUNCLOC_MEAS_POINT.fromJson(e))
          .toList();
      return result;
    } catch (e) {
      throw e;
    }
  }

  static Future<List<EQUIP_MEAS_POINT>> getAllMeasPointsOfEquipMent(
      String equipNo) async {
    try {
      String whereClause = "${EQUIP_MEAS_POINT.FIELD_EQUNR} = '$equipNo'";
      List<EQUIP_MEAS_POINT> result = (await AppDatabaseManager().select(
              DBInputEntity(EQUIP_MEAS_POINT.TABLE_NAME, {})
                ..setWhereClause(whereClause)))
          .map((e) => EQUIP_MEAS_POINT.fromJson(e))
          .toList();
      return result;
    } catch (e) {
      throw e;
    }
  }
}
