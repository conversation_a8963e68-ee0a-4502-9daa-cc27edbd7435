import 'dart:async';

import 'package:eam/helpers/platform_details.dart';
import 'package:eam/presentation/common_widgets/inkwell.dart';
import 'package:eam/provider/tech_objects/equipment_provider.dart';
import 'package:eam/screens/param/route_param.dart';
import 'package:eam/services/navigation_service.dart';
import 'package:eam/utils/app_notifier.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

import '../../../app_styles/app_styles.dart';
import '../../../widgets/atoms_layer/eam_icons.dart';
import '../../../../utils/app_dimension.dart';
import '../../../../widgets/common_widget.dart';
import 'equipment_details_page.dart';

class EquipmentsPage extends StatefulWidget {
  const EquipmentsPage({Key? key}) : super(key: key);

  @override
  _EquipmentsPageState createState() => _EquipmentsPageState();
}

class _EquipmentsPageState extends State<EquipmentsPage> {
  late EquipmentProvider _equipmentProvider;
  bool timerRunning = false;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    initProvider();
    // Timer.periodic(Duration(seconds: 2), (timer) {
    //   var equipProvider =
    //       Provider.of<EquipmentProvider>(context, listen: false);
    //   if (equipProvider.equipDataList.isEmpty) {
    //     equipProvider.getEquipments();
    //   } else {
    //     timer.cancel();
    //   }
    // });
    AppNotifier().notifySyncStatus((data) {
      if (mounted) {
        if ((data[EventSyncStatusFieldType] == EventSyncStatusTypeInbox &&
            (data[EventSyncStatusFieldInboxCount] == 0 &&
                data[EventSyncStatusFieldSentItemsCount] == 0))) {
          initProvider();
        } else if (data[EventSyncStatusFieldType] == EventSyncStatusTypeSent) {
          if (!timerRunning) {
            timerRunning = true;
            // Timer timer = Timer.periodic(Duration(seconds: 3), (timer) {
            //   SyncEngine().receive();
            // });
            // Future.delayed(Duration(seconds: 30), () {
            //   timer.cancel();
            //   timerRunning = false;
            // });
          }
        } else {}
      }
    });
  }

  void initProvider() {
    var equipProvider = Provider.of<EquipmentProvider>(context, listen: false);
    equipProvider.loadEquipmentList();
  }

  @override
  Widget build(BuildContext context) {
    _equipmentProvider = Provider.of<EquipmentProvider>(context);

    return Column(
      children: [
        Expanded(child: _getEquipmentListWidget()),
      ],
    );
  }

  _getEquipmentListWidget() {
    if (_equipmentProvider.isFetching) {
      return Center(
        child: CircularProgressIndicator(),
      );
    } else if (_equipmentProvider.filteredList.isNotEmpty) {
      return ListView.separated(
        padding: Dimensions.padding(context)
            .copyWith(top: !PlatformDetails.isMobileOrTab(context) ? 15 : 0),
        physics: BouncingScrollPhysics(),
        separatorBuilder: (ctx, index) {
          return Container(
            height: 12,
          );
        },
        itemBuilder: (context, index) {
          return Container(
            decoration: AppStyles.deFaultBoxDecoration,
            child: InkWellCard(
              borderRadius: 8,
              onTap: () {
                _navigateToEquipmentDetails(
                    equipNo: _equipmentProvider.filteredList[index].equnr!);
              },
              child: ListTile(
                leading: EamIcon(iconName: EamIcon.equip).icon(),
                title: Row(                  
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            height: 8,
                          ),
                          Text("${_equipmentProvider.filteredList[index].equnr!}-${_equipmentProvider.filteredList[index].shtxt!}",
                              style: AppStyles.textStyle_14_700w.copyWith(
                                overflow: TextOverflow.ellipsis,
                              )),
                          SizedBox(
                            height: 8,
                          ),
                          Text(
                            "${_equipmentProvider.filteredList[index].super_func_loc!}-${_equipmentProvider.filteredList[index].super_func_loc_desc!}",
                            style: AppStyles.textStyle_14_500w.copyWith(
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          SizedBox(
                            height: 8,
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                        Text(
                          "${_equipmentProvider.filteredList[index].gen_object_type!} - ${_equipmentProvider.filteredList[index].gen_object_type_text!}",
                          style: AppStyles.textStyle_14_500w.copyWith(
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        SizedBox(
                          height: 8,
                        ),
                        Text(
                          "",
                          style: AppStyles.textStyle_14_500w.copyWith(
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ]
                      ),
                    )
                  ],
                ),

                trailing: EamIcon(
                        iconName: EamIcon.arrow_forward, height: 16, width: 16)
                    .icon(),
                // subtitle: Text(_flocProvider.filteredList[index].shtxt!),
              ),
            ),
          );
        },
        itemCount: _equipmentProvider.filteredList.length,
      );
    } else {
      return EamNoRecordWidget();
    }
  }

  void _navigateToEquipmentDetails({required String equipNo}) {
    NavigationService.pushNamed(
      EquipmentDetailsPage.routeName,
      arguments: EquipmentParam(eqno: equipNo),
    );
  }
}
